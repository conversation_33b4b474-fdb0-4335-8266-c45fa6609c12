import pandas as pd
import os

def get_split_and_path(image_id):
    if image_id in train_ids:
        return 'train', os.path.join(train_dir, f"{image_id}.png")
    elif image_id in test_ids:
        return 'test', os.path.join(test_dir, f"{image_id}.png")
    else:
        return None, None

csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/OriginalResolution/VinDr-CXR.csv'
df = pd.read_csv(csv_path)
# print("Top 10 rows:")
# print(df.head(10))
# print("\nColumn names:", df.columns.tolist())

# print("Column data types:")

train_dir = '/home/<USER>/interpret-cxr-challenge/data/vindr-cxr/1.0.0/train'
test_dir = '/home/<USER>/interpret-cxr-challenge/data/vindr-cxr/1.0.0/test'

# Get image IDs (without extension) from train and test folders
train_ids = {os.path.splitext(f)[0] for f in os.listdir(train_dir) if os.path.isfile(os.path.join(train_dir, f))}
test_ids = {os.path.splitext(f)[0] for f in os.listdir(test_dir) if os.path.isfile(os.path.join(test_dir, f))}

# Apply function to get both split and path
df[['split', 'path']] = df['image_id'].apply(lambda x: pd.Series(get_split_and_path(x)))

# Keep only rows with a split assigned
df = df[df['split'].notnull()].reset_index(drop=True)

mask_rows = []
for _, row in df.iterrows():
    if row['Dice RCA (Mean)'] >= 0.8:
        for phrase in ['Left Lung', 'Right Lung', 'Heart']:
            mask_rows.append({
                'mask': row[phrase],
                'phrase': phrase,
                'path': row['path'],
                'split': row['split'],
                'dataset': "vindr"
            })

df_masks = pd.DataFrame(mask_rows)

output_csv = '/home/<USER>/processed_segmentation_datasets/VinDR_processed.csv'
df_masks.to_csv(output_csv, index=False)
print(f"\nSaved to: {output_csv}")

