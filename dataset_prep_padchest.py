import pandas as pd
import os

def get_split_and_path(image_id):
    if image_id in train_ids:
        return 'train', os.path.join(train_dir, f"{image_id}.png")
    elif image_id in test_ids:
        return 'test', os.path.join(test_dir, f"{image_id}.png")
    else:
        return None, None

csv_path = '/home/<USER>/physionet.org/files/chexmask-cxr-segmentation-data/1.0.0/OriginalResolution/Padchest.csv'

df = pd.read_csv(csv_path)
print("Top 10 rows:")
print(df.head(10))
print("\nColumn names:", df.columns.tolist())



image_dir = '/home/<USER>/interpret-cxr-challenge/data/PadChest_GR'

test_csv = '/home/<USER>/nikhileshe/Dataset Exploration/padchestgr_test_formatted.csv'
train_csv = '/home/<USER>/nikhileshe/Dataset Exploration/padchestgr_train_formatted.csv'
val_csv = '/home/<USER>/nikhileshe/Dataset Exploration/padchestgr_validation_formatted.csv'

image_ids = {os.path.splitext(f)[0] for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f))}

# Apply function to get both split and path
df[['split', 'path']] = df['image_id'].apply(lambda x: pd.Series(get_split_and_path(x)))

# Keep only rows with a split assigned
df = df[df['split'].notnull()].reset_index(drop=True)

mask_rows = []
for _, row in df.iterrows():
    if row['Dice RCA (Mean)'] >= 0.8:
        for phrase in ['Left Lung', 'Right Lung', 'Heart']:
            mask_rows.append({
                'mask': row[phrase],
                'phrase': phrase,
                'path': row['path'],
                'split': row['split'],
                'dataset': "vindr"
            })

df_masks = pd.DataFrame(mask_rows)

output_csv = '/home/<USER>/processed_segmentation_datasets/padchest_processed.csv'
df_masks.to_csv(output_csv, index=False)
print(f"\nSaved to: {output_csv}")

